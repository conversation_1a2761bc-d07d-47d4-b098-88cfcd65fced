package com.intermorph.lms.mcp.service;

import com.intermorph.lms.mcp.domain.Course;
import com.intermorph.lms.mcp.repository.CourseRepository;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.Optional;

@Service
@RequiredArgsConstructor
public class CourseService {

    private final CourseRepository repo;

    @Transactional
    public Course create(String title, String teacherId) {
        Course c = new Course();
        c.setTitle(title);
        c.setTeacherId(teacherId);
        return repo.save(c);
    }

    @Transactional(readOnly = true)
    public Optional<Course> find(Long id) {
        return repo.findById(id);
    }
}
