server:
  port: 8080

spring:
  application:
    name: lms-mcp-maven-jdk21

  datasource:
    url: ******************************************************************************************************************************
    username: lifelonguser
    password: hallym_lifelong!2
    driver-class-name: org.mariadb.jdbc.Driver

  jpa:
    hibernate:
      ddl-auto: update
    properties:
      hibernate:
        format_sql: true
    open-in-view: false

  ai:
    mcp:
      server:
        enabled: true
        name: lms-mcp-http
        version: 1.0.0
        type: SYNC
        instructions: "LMS 도구/리소스를 HTTP(SSE)로 제공합니다."
        sse-message-endpoint: /mcp/message
        sse-endpoint: /sse
        capabilities:
          tool: true
          resource: true
          prompt: false

management:
  endpoints:
    web:
      exposure:
        include: health,info
