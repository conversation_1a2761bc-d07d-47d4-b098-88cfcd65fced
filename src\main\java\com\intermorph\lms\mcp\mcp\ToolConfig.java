package com.intermorph.lms.mcp.mcp;

import org.springframework.ai.tool.method.MethodToolCallbackProvider;
import org.springframework.ai.tool.ToolCallbackProvider;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

@Configuration
public class ToolConfig {

    @Bean
    public ToolCallbackProvider toolCallbacks(CourseToolService courseTool) {
        return MethodToolCallbackProvider
                .builder()
                .toolObjects(courseTool)
                .build();
    }
}
