-- ============================================================================
-- LMS MCP Server - Complete Database Setup Script
-- ============================================================================
-- 파일명: setup_all.sql
-- 설명: 전체 데이터베이스 설정을 한 번에 실행하는 통합 스크립트
-- 작성일: 2025-01-15
-- 사용법: mysql -u root -p < database/setup_all.sql
-- ============================================================================

-- 실행 시작 메시지
SELECT '============================================================================' AS '';
SELECT 'LMS MCP Server - Database Setup Started' AS '';
SELECT '============================================================================' AS '';

-- ============================================================================
-- 1. 데이터베이스 및 사용자 생성
-- ============================================================================

SELECT 'Step 1: Creating database and user...' AS '';

-- 데이터베이스 생성
CREATE DATABASE IF NOT EXISTS hallym_lifelong 
CHARACTER SET utf8mb4 
COLLATE utf8mb4_unicode_ci;

-- 사용자 생성 및 권한 부여
DROP USER IF EXISTS 'lifelonguser'@'localhost';
CREATE USER 'lifelonguser'@'localhost' IDENTIFIED BY 'hallym_lifelong!2';
GRANT ALL PRIVILEGES ON hallym_lifelong.* TO 'lifelonguser'@'localhost';
FLUSH PRIVILEGES;

SELECT 'Database and user created successfully!' AS '';

-- ============================================================================
-- 2. 데이터베이스 선택 및 테이블 생성
-- ============================================================================

USE hallym_lifelong;

SELECT 'Step 2: Creating tables...' AS '';

-- Course 테이블 생성
CREATE TABLE IF NOT EXISTS course (
    id BIGINT NOT NULL AUTO_INCREMENT,
    title VARCHAR(200) NOT NULL,
    teacher_id VARCHAR(64) NOT NULL,
    PRIMARY KEY (id)
) ENGINE=InnoDB 
  DEFAULT CHARSET=utf8mb4 
  COLLATE=utf8mb4_unicode_ci
  COMMENT='강의 정보 테이블';

SELECT 'Tables created successfully!' AS '';

-- ============================================================================
-- 3. 인덱스 생성
-- ============================================================================

SELECT 'Step 3: Creating indexes...' AS '';

-- 인덱스 생성
CREATE INDEX IF NOT EXISTS idx_course_teacher_id ON course(teacher_id);
CREATE INDEX IF NOT EXISTS idx_course_title ON course(title);
CREATE INDEX IF NOT EXISTS idx_course_teacher_title ON course(teacher_id, title);

SELECT 'Indexes created successfully!' AS '';

-- ============================================================================
-- 4. 제약조건 추가
-- ============================================================================

SELECT 'Step 4: Adding constraints...' AS '';

-- 제약조건 추가
ALTER TABLE course 
ADD CONSTRAINT IF NOT EXISTS chk_title_length 
CHECK (CHAR_LENGTH(TRIM(title)) >= 1 AND CHAR_LENGTH(title) <= 200);

ALTER TABLE course 
ADD CONSTRAINT IF NOT EXISTS chk_teacher_id_length 
CHECK (CHAR_LENGTH(TRIM(teacher_id)) >= 1 AND CHAR_LENGTH(teacher_id) <= 64);

ALTER TABLE course 
ADD CONSTRAINT IF NOT EXISTS chk_title_not_empty 
CHECK (TRIM(title) != '');

ALTER TABLE course 
ADD CONSTRAINT IF NOT EXISTS chk_teacher_id_not_empty 
CHECK (TRIM(teacher_id) != '');

SELECT 'Constraints added successfully!' AS '';

-- ============================================================================
-- 5. 기본 샘플 데이터 삽입 (선택사항)
-- ============================================================================

SELECT 'Step 5: Inserting sample data...' AS '';

-- 기본 샘플 데이터
INSERT IGNORE INTO course (title, teacher_id) VALUES 
('Spring Boot 기초', 'teacher001'),
('Java 프로그래밍', 'teacher002'),
('데이터베이스 설계', 'teacher001'),
('웹 개발 실무', 'teacher003'),
('React 개발 실무', 'teacher003'),
('Python 프로그래밍', 'teacher002');

SELECT 'Sample data inserted successfully!' AS '';

-- ============================================================================
-- 6. 설정 확인
-- ============================================================================

SELECT 'Step 6: Verifying setup...' AS '';

-- 데이터베이스 확인
SELECT 'Database verification:' AS '';
SHOW DATABASES LIKE 'hallym_lifelong';

-- 사용자 확인
SELECT 'User verification:' AS '';
SELECT User, Host FROM mysql.user WHERE User = 'lifelonguser';

-- 테이블 구조 확인
SELECT 'Table structure:' AS '';
DESCRIBE course;

-- 인덱스 확인
SELECT 'Indexes:' AS '';
SHOW INDEX FROM course;

-- 데이터 확인
SELECT 'Sample data verification:' AS '';
SELECT COUNT(*) AS total_courses FROM course;
SELECT id, title, teacher_id FROM course LIMIT 5;

-- ============================================================================
-- 7. 완료 메시지
-- ============================================================================

SELECT '============================================================================' AS '';
SELECT 'LMS MCP Server - Database Setup Completed Successfully!' AS '';
SELECT '============================================================================' AS '';

SELECT 'Database Information:' AS '';
SELECT 'Database Name: hallym_lifelong' AS '';
SELECT 'Username: lifelonguser' AS '';
SELECT 'Password: hallym_lifelong!2' AS '';
SELECT 'Character Set: utf8mb4' AS '';
SELECT 'Collation: utf8mb4_unicode_ci' AS '';

SELECT 'Connection String for application.yml:' AS '';
SELECT 'url: ******************************************************************************************************************************' AS '';

SELECT 'Next Steps:' AS '';
SELECT '1. Update application.yml with database connection info' AS '';
SELECT '2. Start the Spring Boot application' AS '';
SELECT '3. Test MCP tools: create_course and find_course' AS '';

SELECT '============================================================================' AS '';
