package com.intermorph.lms.mcp.mcp;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.intermorph.lms.mcp.domain.Course;
import com.intermorph.lms.mcp.service.CourseService;
import lombok.RequiredArgsConstructor;
import org.springframework.ai.tool.Tool;
import org.springframework.stereotype.Service;

import java.util.Map;

@Service
@RequiredArgsConstructor
public class CourseToolService {

    private final CourseService courseService;
    private final ObjectMapper objectMapper;

    @Tool(name = "create_course", description = "강의를 생성하고 DB에 저장합니다")
    public String createCourse(String title, String teacherId) {
        try {
            // 입력 검증
            if (title == null || title.trim().isEmpty()) {
                return objectMapper.writeValueAsString(Map.of(
                    "status", "ERROR",
                    "message", "Title cannot be null or empty"
                ));
            }
            if (teacherId == null || teacherId.trim().isEmpty()) {
                return objectMapper.writeValueAsString(Map.of(
                    "status", "ERROR",
                    "message", "TeacherId cannot be null or empty"
                ));
            }

            Course c = courseService.create(title.trim(), teacherId.trim());
            return objectMapper.writeValueAsString(Map.of(
                "status", "OK",
                "courseId", c.getId(),
                "title", c.getTitle()
            ));
        } catch (JsonProcessingException e) {
            return "{\"status\":\"ERROR\",\"message\":\"JSON processing error\"}";
        } catch (Exception e) {
            return "{\"status\":\"ERROR\",\"message\":\"" + e.getMessage() + "\"}";
        }
    }

    @Tool(name = "find_course", description = "courseId로 강의를 조회합니다")
    public String findCourse(Long courseId) {
        try {
            // 입력 검증
            if (courseId == null) {
                return objectMapper.writeValueAsString(Map.of(
                    "status", "ERROR",
                    "message", "CourseId cannot be null"
                ));
            }

            return courseService.find(courseId)
                    .map(c -> {
                        try {
                            return objectMapper.writeValueAsString(Map.of(
                                "status", "OK",
                                "courseId", c.getId(),
                                "title", c.getTitle(),
                                "teacherId", c.getTeacherId()
                            ));
                        } catch (JsonProcessingException e) {
                            return "{\"status\":\"ERROR\",\"message\":\"JSON processing error\"}";
                        }
                    })
                    .orElseGet(() -> {
                        try {
                            return objectMapper.writeValueAsString(Map.of(
                                "status", "NOT_FOUND",
                                "courseId", courseId
                            ));
                        } catch (JsonProcessingException e) {
                            return "{\"status\":\"ERROR\",\"message\":\"JSON processing error\"}";
                        }
                    });
        } catch (Exception e) {
            return "{\"status\":\"ERROR\",\"message\":\"" + e.getMessage() + "\"}";
        }
    }
}
