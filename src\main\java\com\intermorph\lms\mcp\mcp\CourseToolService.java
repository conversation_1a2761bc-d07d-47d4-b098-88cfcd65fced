package com.intermorph.lms.mcp.mcp;

import com.intermorph.lms.mcp.domain.Course;
import com.intermorph.lms.mcp.service.CourseService;
import lombok.RequiredArgsConstructor;
import org.springframework.ai.tool.Tool;
import org.springframework.stereotype.Service;

@Service
@RequiredArgsConstructor
public class CourseToolService {

    private final CourseService courseService;

    @Tool(name = "create_course", description = "강의를 생성하고 DB에 저장합니다")
    public String createCourse(String title, String teacherId) {
        Course c = courseService.create(title, teacherId);
        return "{"
                + "\"status\":\"OK\","
                + "\"courseId\":" + c.getId() + ","
                + "\"title\":\"" + c.getTitle() + "\""
                + "}";
    }

    @Tool(name = "find_course", description = "courseId로 강의를 조회합니다")
    public String findCourse(Long courseId) {
        return courseService.find(courseId)
                .map(c -> "{"
                        + "\"status\":\"OK\","
                        + "\"courseId\":" + c.getId() + ","
                        + "\"title\":\"" + c.getTitle() + "\","
                        + "\"teacherId\":\"" + c.getTeacherId() + "\""
                        + "}")
                .orElse("{"
                        + "\"status\":\"NOT_FOUND\","
                        + "\"courseId\":" + courseId
                        + "}");
    }
}
