-- ============================================================================
-- LMS MCP Server - Sample Data Script
-- ============================================================================
-- 파일명: 03_sample_data.sql
-- 설명: 테스트용 샘플 데이터 삽입 스크립트
-- 작성일: 2025-01-15
-- ============================================================================

-- 데이터베이스 선택
USE hallym_lifelong;

-- ============================================================================
-- 1. 기존 데이터 정리 (선택사항)
-- ============================================================================

-- 기존 샘플 데이터 삭제 (주의: 실제 데이터가 있다면 주석 처리)
-- DELETE FROM course WHERE teacher_id IN ('teacher001', 'teacher002', 'teacher003', 'teacher004', 'teacher005');

-- ============================================================================
-- 2. 샘플 강의 데이터 삽입
-- ============================================================================

-- 프로그래밍 관련 강의
INSERT INTO course (title, teacher_id) VALUES 
('Spring Boot 기초', 'teacher001'),
('Spring Boot 심화', 'teacher001'),
('Java 프로그래밍 입문', 'teacher002'),
('Java 고급 프로그래밍', 'teacher002'),
('JavaScript 기초', 'teacher003'),
('React 개발 실무', 'teacher003');

-- 데이터베이스 관련 강의
INSERT INTO course (title, teacher_id) VALUES 
('데이터베이스 설계', 'teacher004'),
('SQL 기초와 활용', 'teacher004'),
('NoSQL 데이터베이스', 'teacher004');

-- 웹 개발 관련 강의
INSERT INTO course (title, teacher_id) VALUES 
('웹 개발 실무', 'teacher005'),
('HTML/CSS 기초', 'teacher005'),
('RESTful API 설계', 'teacher001'),
('마이크로서비스 아키텍처', 'teacher001');

-- AI/ML 관련 강의
INSERT INTO course (title, teacher_id) VALUES 
('머신러닝 기초', 'teacher002'),
('딥러닝 실무', 'teacher002'),
('자연어처리 입문', 'teacher003');

-- 기타 IT 강의
INSERT INTO course (title, teacher_id) VALUES 
('Docker와 컨테이너', 'teacher004'),
('Kubernetes 실무', 'teacher004'),
('DevOps 실무', 'teacher005'),
('클라우드 컴퓨팅', 'teacher005');

-- ============================================================================
-- 3. 다양한 케이스 테스트용 데이터
-- ============================================================================

-- 긴 제목 테스트 (200자 제한 테스트)
INSERT INTO course (title, teacher_id) VALUES 
('매우 긴 강의 제목 테스트용 데이터입니다. 이 강의는 Spring Boot와 React를 활용한 풀스택 웹 애플리케이션 개발 과정으로, 백엔드 API 개발부터 프론트엔드 UI 구현까지 전체적인 웹 개발 프로세스를 다룹니다. 실무에서 바로 활용할 수 있는 실전 예제와 함께 진행됩니다.', 'teacher001');

-- 특수문자 포함 제목 테스트
INSERT INTO course (title, teacher_id) VALUES 
('C++ 프로그래밍 & 알고리즘', 'teacher002'),
('Python 3.x 완전정복!', 'teacher003'),
('Node.js + Express.js 웹서버 개발', 'teacher004');

-- 한글/영문 혼합 제목 테스트
INSERT INTO course (title, teacher_id) VALUES 
('AWS Cloud 실무 활용법', 'teacher005'),
('Git & GitHub 협업 가이드', 'teacher001'),
('Agile 방법론과 Scrum 실무', 'teacher002');

-- ============================================================================
-- 4. 데이터 삽입 확인
-- ============================================================================

-- 전체 강의 수 확인
SELECT COUNT(*) AS total_courses FROM course;

-- 강사별 강의 수 확인
SELECT teacher_id, COUNT(*) AS course_count 
FROM course 
GROUP BY teacher_id 
ORDER BY course_count DESC;

-- 최근 삽입된 강의 확인 (상위 10개)
SELECT id, title, teacher_id 
FROM course 
ORDER BY id DESC 
LIMIT 10;

-- 제목 길이별 분포 확인
SELECT 
    CASE 
        WHEN CHAR_LENGTH(title) <= 20 THEN '짧음 (1-20자)'
        WHEN CHAR_LENGTH(title) <= 50 THEN '보통 (21-50자)'
        WHEN CHAR_LENGTH(title) <= 100 THEN '긴편 (51-100자)'
        ELSE '매우긴편 (101자 이상)'
    END AS title_length_category,
    COUNT(*) AS count
FROM course 
GROUP BY title_length_category
ORDER BY count DESC;

SELECT 'Sample data inserted successfully' AS status;
