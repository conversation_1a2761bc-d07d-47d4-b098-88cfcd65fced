-- ============================================================================
-- LMS MCP Server - Database Setup Script
-- ============================================================================
-- 파일명: 01_create_database.sql
-- 설명: 데이터베이스 및 사용자 생성 스크립트
-- 작성일: 2025-01-15
-- ============================================================================

-- 1. 데이터베이스 생성
-- UTF8MB4 문자셋과 유니코드 콜레이션 사용 (이모지 지원)
CREATE DATABASE IF NOT EXISTS hallym_lifelong 
CHARACTER SET utf8mb4 
COLLATE utf8mb4_unicode_ci;

-- 2. 사용자 생성 및 권한 부여
-- 기존 사용자가 있다면 삭제 후 재생성
DROP USER IF EXISTS 'lifelonguser'@'localhost';
CREATE USER 'lifelonguser'@'localhost' IDENTIFIED BY 'hallym_lifelong!2';

-- 데이터베이스에 대한 모든 권한 부여
GRANT ALL PRIVILEGES ON hallym_lifelong.* TO 'lifelonguser'@'localhost';

-- 권한 적용
FLUSH PRIVILEGES;

-- 3. 생성된 데이터베이스 및 사용자 확인
SELECT 'Database created successfully' AS status;
SHOW DATABASES LIKE 'hallym_lifelong';

SELECT 'User created successfully' AS status;
SELECT User, Host FROM mysql.user WHERE User = 'lifelonguser';

-- 4. 데이터베이스 선택
USE hallym_lifelong;

SELECT 'Setup completed successfully' AS final_status;
