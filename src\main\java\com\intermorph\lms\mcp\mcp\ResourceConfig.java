package com.intermorph.lms.mcp.mcp;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.intermorph.lms.mcp.service.CourseService;
import io.modelcontextprotocol.java.McpSchema;
import io.modelcontextprotocol.java.server.McpServerFeatures;
import io.modelcontextprotocol.java.server.sync.McpSyncServerExchange;
import lombok.RequiredArgsConstructor;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

import java.util.List;
import java.util.Map;

@Configuration
@RequiredArgsConstructor
public class ResourceConfig {

    private final CourseService courseService;

    @Bean
    public List<McpServerFeatures.SyncResourceSpecification> eduResources() {
        var templated = new McpSchema.ResourceTemplate(
                "edu://courses/{courseId}",
                "Course Summary",
                "강의 요약 리소스",
                "application/json"
        );

        var spec = new McpServerFeatures.SyncResourceTemplateSpecification(
                templated,
                (McpSyncServerExchange exchange, McpSchema.ReadResourceRequest req, Map<String,String> params) -> {
                    try {
                        String courseIdParam = params.get("courseId");
                        if (courseIdParam == null || courseIdParam.trim().isEmpty()) {
                            var errorPayload = Map.of(
                                "status", "ERROR",
                                "message", "courseId parameter is required"
                            );
                            String json = new ObjectMapper().writeValueAsString(errorPayload);
                            return new McpSchema.ReadResourceResult(
                                    List.of(new McpSchema.TextResourceContents(
                                            req.uri(), "application/json", json))
                            );
                        }

                        Long id;
                        try {
                            id = Long.valueOf(courseIdParam.trim());
                        } catch (NumberFormatException e) {
                            var errorPayload = Map.of(
                                "status", "ERROR",
                                "message", "Invalid courseId format: " + courseIdParam
                            );
                            String json = new ObjectMapper().writeValueAsString(errorPayload);
                            return new McpSchema.ReadResourceResult(
                                    List.of(new McpSchema.TextResourceContents(
                                            req.uri(), "application/json", json))
                            );
                        }

                        var mapper = new ObjectMapper();
                        var payload = courseService.find(id)
                                .map(c -> Map.of(
                                        "courseId", c.getId(),
                                        "title", c.getTitle(),
                                        "teacherId", c.getTeacherId()
                                ))
                                .orElse(Map.of(
                                        "status", "NOT_FOUND",
                                        "courseId", id
                                ));
                        String json = mapper.writeValueAsString(payload);
                        return new McpSchema.ReadResourceResult(
                                List.of(new McpSchema.TextResourceContents(
                                        req.uri(), "application/json", json))
                        );
                    } catch (Exception e) {
                        try {
                            var errorPayload = Map.of(
                                "status", "ERROR",
                                "message", "Failed to read resource: " + e.getMessage()
                            );
                            String json = new ObjectMapper().writeValueAsString(errorPayload);
                            return new McpSchema.ReadResourceResult(
                                    List.of(new McpSchema.TextResourceContents(
                                            req.uri(), "application/json", json))
                            );
                        } catch (Exception jsonException) {
                            throw new RuntimeException("Failed to read resource and create error response", e);
                        }
                    }
                });

        return List.of(spec);
    }
}
