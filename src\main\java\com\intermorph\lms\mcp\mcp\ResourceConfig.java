package com.intermorph.lms.mcp.mcp;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.intermorph.lms.mcp.service.CourseService;
import io.modelcontextprotocol.spec.McpSchema;
import io.modelcontextprotocol.server.McpServerFeatures;
import io.modelcontextprotocol.server.McpSyncServerExchange;
import lombok.RequiredArgsConstructor;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

import java.util.List;
import java.util.Map;

@Configuration
@RequiredArgsConstructor
public class ResourceConfig {

    private final CourseService courseService;

    @Bean
    public List<McpServerFeatures.SyncResourceSpecification> eduResources() {
        // 템플릿 URI: edu://courses/{courseId}
        var templated = new McpSchema.ResourceTemplate(
                "edu://courses/{courseId}",
                "Course Summary",
                "강의 요약 리소스",
                "application/json"
        );

        var spec = new McpServerFeatures.SyncResourceTemplateSpecification(
                templated,
                (McpSyncServerExchange exchange,
                 McpSchema.ReadResourceRequest req,
                 Map<String, String> params) -> {

                    try {
                        Long id = Long.valueOf(params.get("courseId"));
                        var mapper = new ObjectMapper();
                        var payload = courseService.find(id)
                                .map(c -> Map.of(
                                        "courseId", c.getId(),
                                        "title", c.getTitle(),
                                        "teacherId", c.getTeacherId()
                                ))
                                .orElse(Map.of(
                                        "status", "NOT_FOUND",
                                        "courseId", id
                                ));
                        String json = mapper.writeValueAsString(payload);

                        return new McpSchema.ReadResourceResult(
                                List.of(new McpSchema.TextResourceContents(
                                        req.uri(), "application/json", json))
                        );
                    } catch (Exception e) {
                        throw new RuntimeException("Failed to read resource", e);
                    }
                });

        return List.of(spec);
    }
}
