-- ============================================================================
-- LMS MCP Server - Table Creation Script
-- ============================================================================
-- 파일명: 02_create_tables.sql
-- 설명: 테이블 생성 및 제약조건 설정 스크립트
-- 작성일: 2025-01-15
-- ============================================================================

-- 데이터베이스 선택
USE hallym_lifelong;

-- ============================================================================
-- 1. Course 테이블 생성
-- ============================================================================

-- 기존 테이블이 있다면 삭제 (주의: 데이터 손실 발생)
-- DROP TABLE IF EXISTS course;

-- Course 테이블 생성
CREATE TABLE IF NOT EXISTS course (
    -- 기본키: 자동 증가하는 고유 식별자
    id BIGINT NOT NULL AUTO_INCREMENT,
    
    -- 강의 제목: 최대 200자, 필수 입력
    title VARCHAR(200) NOT NULL,
    
    -- 강사 식별자: 최대 64자, 필수 입력
    teacher_id VARCHAR(64) NOT NULL,
    
    -- 생성일시 (선택사항 - 향후 확장용)
    -- created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    
    -- 수정일시 (선택사항 - 향후 확장용)
    -- updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    
    -- 기본키 설정
    PRIMARY KEY (id)
) ENGINE=InnoDB 
  DEFAULT CHARSET=utf8mb4 
  COLLATE=utf8mb4_unicode_ci
  COMMENT='강의 정보 테이블';

-- ============================================================================
-- 2. 인덱스 생성 (성능 최적화)
-- ============================================================================

-- teacher_id 인덱스 (강사별 강의 조회 최적화)
CREATE INDEX IF NOT EXISTS idx_course_teacher_id ON course(teacher_id);

-- title 인덱스 (강의명 검색 최적화)
CREATE INDEX IF NOT EXISTS idx_course_title ON course(title);

-- 복합 인덱스 (강사별 강의명 조회 최적화)
CREATE INDEX IF NOT EXISTS idx_course_teacher_title ON course(teacher_id, title);

-- ============================================================================
-- 3. 제약조건 추가
-- ============================================================================

-- title 길이 제한 체크 (1자 이상 200자 이하)
ALTER TABLE course 
ADD CONSTRAINT IF NOT EXISTS chk_title_length 
CHECK (CHAR_LENGTH(TRIM(title)) >= 1 AND CHAR_LENGTH(title) <= 200);

-- teacher_id 길이 제한 체크 (1자 이상 64자 이하)
ALTER TABLE course 
ADD CONSTRAINT IF NOT EXISTS chk_teacher_id_length 
CHECK (CHAR_LENGTH(TRIM(teacher_id)) >= 1 AND CHAR_LENGTH(teacher_id) <= 64);

-- title 공백 방지 체크
ALTER TABLE course 
ADD CONSTRAINT IF NOT EXISTS chk_title_not_empty 
CHECK (TRIM(title) != '');

-- teacher_id 공백 방지 체크
ALTER TABLE course 
ADD CONSTRAINT IF NOT EXISTS chk_teacher_id_not_empty 
CHECK (TRIM(teacher_id) != '');

-- ============================================================================
-- 4. 선택적 제약조건 (필요시 주석 해제)
-- ============================================================================

-- 강의명 중복 방지 (전체 시스템에서 유일한 강의명)
-- ALTER TABLE course 
-- ADD CONSTRAINT uk_course_title UNIQUE (title);

-- 강사별 강의명 중복 방지 (같은 강사가 동일한 이름의 강의 생성 방지)
-- ALTER TABLE course 
-- ADD CONSTRAINT uk_course_teacher_title UNIQUE (teacher_id, title);

-- ============================================================================
-- 5. 테이블 생성 확인
-- ============================================================================

-- 테이블 구조 확인
DESCRIBE course;

-- 인덱스 확인
SHOW INDEX FROM course;

-- 테이블 상태 확인
SHOW TABLE STATUS LIKE 'course';

SELECT 'Course table created successfully' AS status;
