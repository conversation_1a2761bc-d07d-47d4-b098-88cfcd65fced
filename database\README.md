# 📊 LMS MCP Server - Database Scripts

이 디렉토리는 LMS MCP Server 프로젝트의 데이터베이스 설정 및 관리를 위한 SQL 스크립트들을 포함합니다.

## 📁 파일 구조

```
database/
├── README.md                 # 이 파일
├── 01_create_database.sql    # 데이터베이스 및 사용자 생성
├── 02_create_tables.sql      # 테이블 생성 및 제약조건 설정
├── 03_sample_data.sql        # 샘플 데이터 삽입
├── 04_queries.sql            # 유용한 쿼리 모음
└── setup_all.sql             # 전체 설정 스크립트 (통합)
```

## 🚀 빠른 시작

### 1. 전체 설정 (권장)
```bash
mysql -u root -p < database/setup_all.sql
```

### 2. 단계별 설정
```bash
# 1단계: 데이터베이스 및 사용자 생성
mysql -u root -p < database/01_create_database.sql

# 2단계: 테이블 생성
mysql -u root -p < database/02_create_tables.sql

# 3단계: 샘플 데이터 삽입 (선택사항)
mysql -u root -p < database/03_sample_data.sql
```

## 📋 스크립트 상세 설명

### 🗄️ 01_create_database.sql
- **목적**: 데이터베이스 및 사용자 계정 생성
- **내용**:
  - `hallym_lifelong` 데이터베이스 생성 (UTF8MB4)
  - `lifelonguser` 사용자 생성 및 권한 부여
  - 설정 확인 쿼리

### 🏗️ 02_create_tables.sql
- **목적**: 테이블 생성 및 최적화
- **내용**:
  - `course` 테이블 생성
  - 인덱스 생성 (성능 최적화)
  - 제약조건 설정 (데이터 무결성)
  - 테이블 구조 확인

### 📝 03_sample_data.sql
- **목적**: 개발 및 테스트용 샘플 데이터 제공
- **내용**:
  - 다양한 카테고리의 강의 데이터
  - 특수 케이스 테스트 데이터
  - 데이터 검증 쿼리

### 🔍 04_queries.sql
- **목적**: 개발 및 운영에 유용한 쿼리 모음
- **내용**:
  - 기본 CRUD 쿼리
  - 통계 및 분석 쿼리
  - 데이터 검증 쿼리
  - 성능 모니터링 쿼리

## 🗃️ 데이터베이스 스키마

### Course 테이블
| 컬럼명 | 타입 | 제약조건 | 설명 |
|--------|------|----------|------|
| `id` | `BIGINT` | `PRIMARY KEY`, `AUTO_INCREMENT` | 강의 고유 식별자 |
| `title` | `VARCHAR(200)` | `NOT NULL` | 강의 제목 |
| `teacher_id` | `VARCHAR(64)` | `NOT NULL` | 강사 식별자 |

### 인덱스
- `PRIMARY KEY (id)`: 기본키 인덱스
- `idx_course_teacher_id`: 강사 ID 인덱스
- `idx_course_title`: 강의 제목 인덱스
- `idx_course_teacher_title`: 복합 인덱스 (강사 ID + 제목)

## ⚙️ 설정 정보

### 데이터베이스 연결 정보
```yaml
spring:
  datasource:
    url: *********************************************
    username: lifelonguser
    password: hallym_lifelong!2
    driver-class-name: org.mariadb.jdbc.Driver
```

### 문자셋 설정
- **Character Set**: `utf8mb4`
- **Collation**: `utf8mb4_unicode_ci`
- **이모지 지원**: ✅

## 🛡️ 보안 고려사항

### 사용자 권한
- `lifelonguser`: `hallym_lifelong` 데이터베이스에 대한 모든 권한
- 최소 권한 원칙 적용 권장

### 비밀번호 정책
- 현재 비밀번호: `hallym_lifelong!2`
- 운영 환경에서는 더 강력한 비밀번호 사용 권장

## 🔧 유지보수

### 백업
```bash
# 데이터베이스 전체 백업
mysqldump -u lifelonguser -p hallym_lifelong > backup_$(date +%Y%m%d_%H%M%S).sql

# 테이블별 백업
mysqldump -u lifelonguser -p hallym_lifelong course > course_backup_$(date +%Y%m%d_%H%M%S).sql
```

### 복원
```bash
# 전체 복원
mysql -u lifelonguser -p hallym_lifelong < backup_file.sql

# 테이블별 복원
mysql -u lifelonguser -p hallym_lifelong < course_backup_file.sql
```

## 📊 모니터링

### 성능 확인
```sql
-- 테이블 크기 확인
SELECT table_rows, data_length, index_length 
FROM information_schema.tables 
WHERE table_schema = 'hallym_lifelong' AND table_name = 'course';

-- 인덱스 사용률 확인
SHOW INDEX FROM course;
```

### 데이터 무결성 확인
```sql
-- 제약조건 위반 확인
SELECT * FROM course WHERE title IS NULL OR teacher_id IS NULL;
SELECT * FROM course WHERE CHAR_LENGTH(title) > 200 OR CHAR_LENGTH(teacher_id) > 64;
```

## 🚨 주의사항

1. **운영 환경**: 스크립트 실행 전 반드시 백업 수행
2. **권한 관리**: 최소 권한 원칙 적용
3. **비밀번호**: 운영 환경에서는 강력한 비밀번호 사용
4. **데이터 삭제**: 삭제 관련 쿼리는 신중히 사용

## 📞 문의

데이터베이스 관련 문의사항이 있으시면 프로젝트 이슈를 생성해 주세요.
