-- ============================================================================
-- LMS MCP Server - Useful Queries Script
-- ============================================================================
-- 파일명: 04_queries.sql
-- 설명: 개발 및 운영에 유용한 쿼리 모음
-- 작성일: 2025-01-15
-- ============================================================================

-- 데이터베이스 선택
USE hallym_lifelong;

-- ============================================================================
-- 1. 기본 조회 쿼리
-- ============================================================================

-- 전체 강의 조회
SELECT id, title, teacher_id FROM course ORDER BY id;

-- 특정 강의 조회 (ID로)
SELECT id, title, teacher_id FROM course WHERE id = 1;

-- 특정 강사의 강의 조회
SELECT id, title, teacher_id FROM course WHERE teacher_id = 'teacher001';

-- 강의명으로 검색 (부분 일치)
SELECT id, title, teacher_id FROM course WHERE title LIKE '%Spring%';

-- ============================================================================
-- 2. 통계 쿼리
-- ============================================================================

-- 전체 강의 수
SELECT COUNT(*) AS total_courses FROM course;

-- 강사별 강의 수
SELECT 
    teacher_id,
    COUNT(*) AS course_count
FROM course 
GROUP BY teacher_id 
ORDER BY course_count DESC;

-- 강의명 길이 통계
SELECT 
    MIN(CHAR_LENGTH(title)) AS min_title_length,
    MAX(CHAR_LENGTH(title)) AS max_title_length,
    AVG(CHAR_LENGTH(title)) AS avg_title_length
FROM course;

-- 강사 ID 길이 통계
SELECT 
    MIN(CHAR_LENGTH(teacher_id)) AS min_teacher_id_length,
    MAX(CHAR_LENGTH(teacher_id)) AS max_teacher_id_length,
    AVG(CHAR_LENGTH(teacher_id)) AS avg_teacher_id_length
FROM course;

-- ============================================================================
-- 3. 데이터 검증 쿼리
-- ============================================================================

-- 빈 제목이나 강사 ID 확인
SELECT id, title, teacher_id 
FROM course 
WHERE TRIM(title) = '' OR TRIM(teacher_id) = '' OR title IS NULL OR teacher_id IS NULL;

-- 중복 강의명 확인
SELECT title, COUNT(*) AS duplicate_count
FROM course 
GROUP BY title 
HAVING COUNT(*) > 1;

-- 강사별 중복 강의명 확인
SELECT teacher_id, title, COUNT(*) AS duplicate_count
FROM course 
GROUP BY teacher_id, title 
HAVING COUNT(*) > 1;

-- 제목 길이 제한 위반 확인 (200자 초과)
SELECT id, title, CHAR_LENGTH(title) AS title_length
FROM course 
WHERE CHAR_LENGTH(title) > 200;

-- 강사 ID 길이 제한 위반 확인 (64자 초과)
SELECT id, teacher_id, CHAR_LENGTH(teacher_id) AS teacher_id_length
FROM course 
WHERE CHAR_LENGTH(teacher_id) > 64;

-- ============================================================================
-- 4. 성능 테스트 쿼리
-- ============================================================================

-- 인덱스 사용 확인 (EXPLAIN 사용)
EXPLAIN SELECT * FROM course WHERE teacher_id = 'teacher001';
EXPLAIN SELECT * FROM course WHERE title LIKE 'Spring%';
EXPLAIN SELECT * FROM course WHERE teacher_id = 'teacher001' AND title LIKE 'Spring%';

-- 테이블 크기 확인
SELECT 
    table_name,
    table_rows,
    data_length,
    index_length,
    (data_length + index_length) AS total_size
FROM information_schema.tables 
WHERE table_schema = 'hallym_lifelong' AND table_name = 'course';

-- ============================================================================
-- 5. 관리용 쿼리
-- ============================================================================

-- 특정 강사의 모든 강의 삭제 (주의: 실제 운영에서는 신중히 사용)
-- DELETE FROM course WHERE teacher_id = 'teacher_to_delete';

-- 특정 강의 수정
-- UPDATE course SET title = '새로운 강의명' WHERE id = 1;

-- 강사 ID 변경 (일괄 변경)
-- UPDATE course SET teacher_id = 'new_teacher_id' WHERE teacher_id = 'old_teacher_id';

-- ============================================================================
-- 6. 백업 및 복원용 쿼리
-- ============================================================================

-- 테이블 백업 (CREATE TABLE AS SELECT)
-- CREATE TABLE course_backup AS SELECT * FROM course;

-- 데이터 내보내기 (INSERT 문 생성)
-- SELECT CONCAT('INSERT INTO course (title, teacher_id) VALUES (''', title, ''', ''', teacher_id, ''');') AS insert_statement
-- FROM course;

-- ============================================================================
-- 7. 개발 테스트용 쿼리
-- ============================================================================

-- MCP 도구 테스트용 - 강의 생성 시뮬레이션
-- INSERT INTO course (title, teacher_id) VALUES ('테스트 강의', 'test_teacher');

-- MCP 도구 테스트용 - 강의 조회 시뮬레이션
-- SELECT id, title, teacher_id FROM course WHERE id = LAST_INSERT_ID();

-- 랜덤 강의 조회 (테스트용)
SELECT id, title, teacher_id 
FROM course 
ORDER BY RAND() 
LIMIT 1;

-- 최신 강의 조회
SELECT id, title, teacher_id 
FROM course 
ORDER BY id DESC 
LIMIT 5;

-- ============================================================================
-- 8. 모니터링용 쿼리
-- ============================================================================

-- 테이블 상태 확인
SHOW TABLE STATUS LIKE 'course';

-- 인덱스 사용률 확인
SHOW INDEX FROM course;

-- 프로세스 목록 확인 (현재 실행 중인 쿼리)
-- SHOW PROCESSLIST;

-- 데이터베이스 크기 확인
SELECT 
    table_schema AS 'Database',
    SUM(data_length + index_length) / 1024 / 1024 AS 'Size (MB)'
FROM information_schema.tables 
WHERE table_schema = 'hallym_lifelong'
GROUP BY table_schema;

SELECT 'Queries script completed' AS status;
