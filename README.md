# LMS MCP Server

## 📋 프로젝트 개요

LMS (Learning Management System) MCP Server는 **Model Context Protocol (MCP)**를 사용하여 강의 관리 기능을 제공하는 Spring Boot 애플리케이션입니다. AI 모델이 강의 생성, 조회 등의 LMS 기능을 도구(Tool)와 리소스(Resource)로 활용할 수 있도록 합니다.

## 🛠 기술 스택

- **Java**: 21
- **Spring Boot**: 3.3.3
- **Spring AI**: 1.0.0-M6
- **Database**: MariaDB
- **ORM**: Spring Data JPA (Hibernate)
- **Build Tool**: Maven
- **Protocol**: Model Context Protocol (MCP)
- **Transport**: HTTP Server-Sent Events (SSE)

## 🏗 프로젝트 구조

```
src/main/java/com/intermorph/lms/mcp/
├── LmsMcpServerApplication.java     # 메인 애플리케이션 클래스
├── domain/
│   └── Course.java                  # 강의 엔티티
├── repository/
│   └── CourseRepository.java        # 강의 데이터 접근 계층
├── service/
│   └── CourseService.java           # 강의 비즈니스 로직
└── mcp/
    ├── CourseToolService.java       # MCP 도구 서비스
    ├── ToolConfig.java              # 도구 설정
    └── ResourceConfig.java          # 리소스 설정
```

## ⚙️ 주요 기능

### 🔧 MCP Tools (도구)
- **create_course**: 새로운 강의를 생성하고 데이터베이스에 저장
- **find_course**: courseId로 강의 정보를 조회

### 📚 MCP Resources (리소스)
- **edu://courses/{courseId}**: 특정 강의의 상세 정보를 JSON 형태로 제공

### 🌐 HTTP Endpoints
- **SSE Endpoint**: `/sse` - MCP 클라이언트 연결
- **Message Endpoint**: `/mcp/message` - MCP 메시지 처리
- **Health Check**: `/actuator/health` - 애플리케이션 상태 확인
- **Info**: `/actuator/info` - 애플리케이션 정보

## 🚀 시작하기

### 📋 사전 요구사항

1. **Java 21** 설치
2. **MariaDB** 설치 및 실행
3. **Maven** 설치 (또는 IDE의 내장 Maven 사용)

### 🗄 데이터베이스 설정

MariaDB에 다음과 같이 데이터베이스와 사용자를 생성하세요:

```sql
CREATE DATABASE lms CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
CREATE USER 'lms_user'@'localhost' IDENTIFIED BY 'lms_pass';
GRANT ALL PRIVILEGES ON lms.* TO 'lms_user'@'localhost';
FLUSH PRIVILEGES;
```

### 🔧 설정 파일

`src/main/resources/application.yml` 파일에서 데이터베이스 연결 정보를 확인/수정하세요:

```yaml
spring:
  datasource:
    url: ******************************************************************************************************************
    username: lms_user
    password: lms_pass
    driver-class-name: org.mariadb.jdbc.Driver
```

### 🏃‍♂️ 애플리케이션 실행

1. **프로젝트 클론 및 이동**
   ```bash
   cd lms-mcp-server
   ```

2. **의존성 설치 및 빌드**
   ```bash
   mvn clean install
   ```

3. **애플리케이션 실행**
   ```bash
   mvn spring-boot:run
   ```
   
   또는 JAR 파일로 실행:
   ```bash
   java -jar target/lms-mcp-maven-jdk21-0.1.0.jar
   ```

4. **실행 확인**
   - 애플리케이션이 `http://localhost:8080`에서 실행됩니다
   - Health Check: `http://localhost:8080/actuator/health`

## 📡 MCP 클라이언트 연결

### SSE 연결
```
GET http://localhost:8080/sse
Accept: text/event-stream
```

### 도구 사용 예시

**강의 생성:**
```json
{
  "method": "tools/call",
  "params": {
    "name": "create_course",
    "arguments": {
      "title": "Spring Boot 기초",
      "teacherId": "teacher001"
    }
  }
}
```

**강의 조회:**
```json
{
  "method": "tools/call",
  "params": {
    "name": "find_course",
    "arguments": {
      "courseId": 1
    }
  }
}
```

### 리소스 접근 예시
```
edu://courses/1
```

## 🔍 API 응답 형식

### 성공 응답
```json
{
  "status": "OK",
  "courseId": 1,
  "title": "Spring Boot 기초",
  "teacherId": "teacher001"
}
```

### 에러 응답
```json
{
  "status": "ERROR",
  "message": "Title cannot be null or empty"
}
```

### 조회 실패 응답
```json
{
  "status": "NOT_FOUND",
  "courseId": 999
}
```

## 🛡 보안 및 검증

- **입력 검증**: 모든 입력 파라미터에 대한 null/빈 값 검증
- **예외 처리**: 포괄적인 예외 처리 및 에러 응답
- **JSON 안전성**: ObjectMapper를 사용한 안전한 JSON 처리
- **트랜잭션 관리**: Spring의 선언적 트랜잭션 관리

## 🔧 개발 환경

### IDE 설정
- **IntelliJ IDEA** 권장
- **Java 21** 프로젝트 SDK 설정
- **Maven** 자동 임포트 활성화

### 로깅
- Hibernate SQL 로깅 활성화 (`hibernate.format_sql: true`)
- Spring Boot 기본 로깅 설정

## 📝 라이선스

이 프로젝트는 교육 및 연구 목적으로 제작되었습니다.

## 🤝 기여하기

1. Fork the Project
2. Create your Feature Branch (`git checkout -b feature/AmazingFeature`)
3. Commit your Changes (`git commit -m 'Add some AmazingFeature'`)
4. Push to the Branch (`git push origin feature/AmazingFeature`)
5. Open a Pull Request

## 🧪 테스트

### 단위 테스트 실행
```bash
mvn test
```

### 통합 테스트
```bash
mvn verify
```

## 🐳 Docker 지원

### Dockerfile 예시
```dockerfile
FROM openjdk:21-jdk-slim

WORKDIR /app
COPY target/lms-mcp-maven-jdk21-0.1.0.jar app.jar

EXPOSE 8080
ENTRYPOINT ["java", "-jar", "app.jar"]
```

### Docker Compose 예시
```yaml
version: '3.8'
services:
  lms-mcp-server:
    build: .
    ports:
      - "8080:8080"
    depends_on:
      - mariadb
    environment:
      - SPRING_DATASOURCE_URL=*******************************
      - SPRING_DATASOURCE_USERNAME=lms_user
      - SPRING_DATASOURCE_PASSWORD=lms_pass

  mariadb:
    image: mariadb:10.11
    environment:
      - MYSQL_ROOT_PASSWORD=rootpass
      - MYSQL_DATABASE=lms
      - MYSQL_USER=lms_user
      - MYSQL_PASSWORD=lms_pass
    ports:
      - "3306:3306"
    volumes:
      - mariadb_data:/var/lib/mysql

volumes:
  mariadb_data:
```

## 🔧 설정 옵션

### MCP 서버 설정
```yaml
spring:
  ai:
    mcp:
      server:
        enabled: true                    # MCP 서버 활성화
        name: lms-mcp-http              # 서버 이름
        version: 1.0.0                  # 서버 버전
        type: SYNC                      # 동기/비동기 타입 (SYNC/ASYNC)
        instructions: "LMS 도구/리소스를 HTTP(SSE)로 제공합니다."
        sse-message-endpoint: /mcp/message  # 메시지 엔드포인트
        sse-endpoint: /sse              # SSE 엔드포인트
        capabilities:
          tool: true                    # 도구 기능 활성화
          resource: true                # 리소스 기능 활성화
          prompt: false                 # 프롬프트 기능 비활성화
```

### 데이터베이스 설정
```yaml
spring:
  jpa:
    hibernate:
      ddl-auto: update                  # 스키마 자동 업데이트
    properties:
      hibernate:
        format_sql: true                # SQL 포맷팅
    open-in-view: false                 # OSIV 비활성화
```

## 🚨 트러블슈팅

### 일반적인 문제들

1. **데이터베이스 연결 실패**
   ```
   해결: MariaDB 서비스 실행 상태 및 연결 정보 확인
   ```

2. **포트 충돌 (8080)**
   ```yaml
   server:
     port: 8081  # 다른 포트로 변경
   ```

3. **Java 버전 불일치**
   ```
   해결: Java 21 설치 및 JAVA_HOME 환경변수 설정
   ```

4. **Maven 의존성 문제**
   ```bash
   mvn clean install -U  # 강제 업데이트
   ```

## 📊 모니터링

### Actuator 엔드포인트
- `/actuator/health` - 애플리케이션 상태
- `/actuator/info` - 애플리케이션 정보
- `/actuator/metrics` - 메트릭 정보 (추가 설정 필요)

### 로그 레벨 설정
```yaml
logging:
  level:
    com.intermorph.lms.mcp: DEBUG
    org.springframework.ai: DEBUG
    org.hibernate.SQL: DEBUG
```

## 🔄 버전 히스토리

- **v0.1.0**: 초기 버전
  - 기본 강의 생성/조회 기능
  - MCP 도구 및 리소스 지원
  - MariaDB 연동

## 📚 참고 자료

- [Model Context Protocol 공식 문서](https://modelcontextprotocol.io/)
- [Spring AI 문서](https://docs.spring.io/spring-ai/reference/)
- [Spring Boot 문서](https://docs.spring.io/spring-boot/docs/current/reference/htmlsingle/)
- [MariaDB 문서](https://mariadb.org/documentation/)

## 📞 문의

프로젝트에 대한 문의사항이 있으시면 이슈를 생성해 주세요.
